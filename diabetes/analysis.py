import marimo

__generated_with = "0.13.15"
app = marimo.App(width="medium")


@app.cell
def _():
    return


@app.cell
def _():
    import sys
    sys.path.append('./../')
    import numpy as np
    import matplotlib.pyplot as plt
    import pandas as pd
    from scipy.signal import welch, detrend, butter, filtfilt, csd
    from scipy import stats
    import sympy as sym

    path = 'diabetes/results/'
    batch_name = 'variability'
    trials = np.arange(50)



    trials_long = np.arange(1, 5)

    conditions = ['normal', 'low_affected', 'severe']

    t_start = 4000
    t_end = 10000
    return (
        batch_name,
        butter,
        conditions,
        csd,
        detrend,
        filtfilt,
        np,
        path,
        pd,
        plt,
        stats,
        t_end,
        t_start,
        trials,
        trials_long,
        welch,
    )


@app.cell
def _(butter, filtfilt, np, plt):
    def select_mns_randomly(data, t_start, t_end, size=4, column_spikes=1):
        steady_data = data[(data[:, column_spikes] >= t_start) & (data[:, column_spikes] <= t_end)]
        unique_neurons = np.unique(data[:, 0])
        fr = compute_fr(unique_neurons, steady_data, t_start, t_end, column_spikes=column_spikes)
        # Filtrar dados da fase de estado estacionário

        # Seleção dos neurônios
        selected_neurons = unique_neurons
        # selected_neurons = selected_neurons[np.where((fr < 200))[0]].astype(int)
        selected_neurons = np.random.choice(selected_neurons, size=size)

        return selected_neurons




    def select_mns_regular(data, t_start, t_end, column_spikes=1):
        unique_neurons = np.unique(data[:, 0])

        # Filtrar dados da fase de estado estacionário
        steady_data = data[(data[:, column_spikes] >= t_start) & (data[:, column_spikes] <= t_end)]
        ISI_CV, ISI_mean = compute_cv(unique_neurons, steady_data, t_start, t_end, column_spikes=column_spikes)

        fr = compute_fr(unique_neurons, data, t_start, t_end, column_spikes=column_spikes)

        # Seleção dos neurônios

        selection_criteria = np.where((fr > 5) & (fr < 15) & (ISI_CV <=0.3))[0]
        selected_neurons = unique_neurons[selection_criteria].astype(int)
        fr = fr[selection_criteria]
        mn_number =6#int(min(np.random.randint(low=4, high=12, size=1)[0], len(fr)))
        if len(selected_neurons) > mn_number:
             selected_neurons = selected_neurons[np.argsort(fr)][:mn_number]
        # print(ISI_CV[selection_criteria])
        return selected_neurons

    def compute_fr(selected_neurons, data, t_start, t_end, column_spikes=1):
        window_duration = (t_end - t_start) / 1000  # s
        steady_data = data[(data[:, column_spikes] >= t_start) & (data[:, column_spikes] <= t_end)]
        firing_rates = np.zeros(len(selected_neurons))
        i = 0
        for neuron in selected_neurons:
            n_spikes = np.sum(steady_data[:, 0] == neuron)
            fr = n_spikes / window_duration
            firing_rates[i] = fr
            i = i + 1
        return firing_rates

    def compute_mn_cv(spike_times, t_start):
        ISI = np.diff(spike_times[spike_times> t_start])
        if len(ISI)>3:
            ISI_SD = ISI.std(ddof=1)
            ISI_mean = ISI.mean()
            ISI_CV = ISI_SD/ISI_mean
        else:
            ISI_mean = 0
            ISI_CV = 1
        return ISI_CV, ISI_mean

    def compute_cv(selected_neurons, data, t_start, t_end, column_spikes=1):
        steady_data = data[(data[:, column_spikes] >= t_start) & (data[:, column_spikes] <= t_end)]
        ISI_CV = np.zeros(len(selected_neurons))
        ISI_mean = np.zeros(len(selected_neurons))
        i = 0
        for neuron in selected_neurons:
            ISI_CV[i], ISI_mean[i] = compute_mn_cv(steady_data[steady_data[:, 0] == neuron, column_spikes], t_start=t_start)
            i = i + 1

        return ISI_CV, ISI_mean


    def plot_mn_fr_combined(mn_rate_mean_mean_regular, mn_rate_mean_CV_regular,
                           mn_rate_mean_mean_randomly, mn_rate_mean_CV_randomly,
                           conditions, pd, stats, p_values_regular, p_values_randomly):
        import os
        os.makedirs('diabetes/figuras', exist_ok=True)

        # Calcular estatísticas para ambos os modos
        mean_fr_regular = np.hstack((np.mean(mn_rate_mean_mean_regular[conditions[0]]),
                                   np.mean(mn_rate_mean_mean_regular[conditions[1]]),
                                   np.mean(mn_rate_mean_mean_regular[conditions[2]])))
        sem_fr_regular = np.hstack((mn_rate_mean_mean_regular[conditions[0]].std()/np.sqrt(len(mn_rate_mean_mean_regular[conditions[0]])),
                                  mn_rate_mean_mean_regular[conditions[1]].std()/np.sqrt(len(mn_rate_mean_mean_regular[conditions[1]])),
                                  mn_rate_mean_mean_regular[conditions[2]].std(ddof=1)/np.sqrt(len(mn_rate_mean_mean_regular[conditions[2]]))))

        mean_fr_randomly = np.hstack((np.mean(mn_rate_mean_mean_randomly[conditions[0]]),
                                    np.mean(mn_rate_mean_mean_randomly[conditions[1]]),
                                    np.mean(mn_rate_mean_mean_randomly[conditions[2]])))
        sem_fr_randomly = np.hstack((mn_rate_mean_mean_randomly[conditions[0]].std()/np.sqrt(len(mn_rate_mean_mean_randomly[conditions[0]])),
                                   mn_rate_mean_mean_randomly[conditions[1]].std()/np.sqrt(len(mn_rate_mean_mean_randomly[conditions[1]])),
                                   mn_rate_mean_mean_randomly[conditions[2]].std(ddof=1)/np.sqrt(len(mn_rate_mean_mean_randomly[conditions[2]]))))

        # Criar figura com dois subplots lado a lado
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

        # Função para adicionar indicações de significância
        def add_significance_bars(ax, means, p_values, y_offset=0.5):
            max_y = max(means) + y_offset

            # Comparação normal vs severe (posições 1 e 3)
            if p_values['normal_vs_severe'] < 0.05:
                y_pos = max_y + 0.3
                ax.plot([1, 3], [y_pos, y_pos], 'k-', linewidth=1)
                ax.plot([1, 1], [y_pos-0.1, y_pos], 'k-', linewidth=1)
                ax.plot([3, 3], [y_pos-0.1, y_pos], 'k-', linewidth=1)
                significance = '***' if p_values['normal_vs_severe'] < 0.001 else '**' if p_values['normal_vs_severe'] < 0.01 else '*'
                ax.text(2, y_pos + 0.1, significance, ha='center', va='bottom', fontweight='bold')
                max_y = y_pos + 0.4

            # Comparação low_affected vs severe (posições 2 e 3)
            if p_values['low_affected_vs_severe'] < 0.05:
                y_pos = max_y + 0.3
                ax.plot([2, 3], [y_pos, y_pos], 'k-', linewidth=1)
                ax.plot([2, 2], [y_pos-0.1, y_pos], 'k-', linewidth=1)
                ax.plot([3, 3], [y_pos-0.1, y_pos], 'k-', linewidth=1)
                significance = '***' if p_values['low_affected_vs_severe'] < 0.001 else '**' if p_values['low_affected_vs_severe'] < 0.01 else '*'
                ax.text(2.5, y_pos + 0.1, significance, ha='center', va='bottom', fontweight='bold')
                max_y = y_pos + 0.4

            # Comparação normal vs low_affected (posições 1 e 2)
            if p_values['normal_vs_low_affected'] < 0.05:
                y_pos = max_y + 0.3
                ax.plot([1, 2], [y_pos, y_pos], 'k-', linewidth=1)
                ax.plot([1, 1], [y_pos-0.1, y_pos], 'k-', linewidth=1)
                ax.plot([2, 2], [y_pos-0.1, y_pos], 'k-', linewidth=1)
                significance = '***' if p_values['normal_vs_low_affected'] < 0.001 else '**' if p_values['normal_vs_low_affected'] < 0.01 else '*'
                ax.text(1.5, y_pos + 0.1, significance, ha='center', va='bottom', fontweight='bold')

        # Plot para modo regular
        ax1.errorbar([1,2,3], mean_fr_regular, fmt='.', yerr=sem_fr_regular, capsize=5, color='black')
        ax1.grid()
        ax1.scatter(1+0.1*np.random.normal(size=len(mn_rate_mean_mean_regular[conditions[0]])), mn_rate_mean_mean_regular[conditions[0]])
        ax1.scatter(2+0.1*np.random.normal(size=len(mn_rate_mean_mean_regular[conditions[1]])), mn_rate_mean_mean_regular[conditions[1]])
        ax1.scatter(3+0.1*np.random.normal(size=len(mn_rate_mean_mean_regular[conditions[2]])), mn_rate_mean_mean_regular[conditions[2]])
        ax1.set_xticks([1,2,3])
        ax1.set_xticklabels([conditions[0], conditions[1], conditions[2]])
        ax1.set_ylabel('MN firing rate (pps)')
        ax1.set_title('Regular Mode')
        add_significance_bars(ax1, mean_fr_regular, p_values_regular)

        # Plot para modo random
        ax2.errorbar([1,2,3], mean_fr_randomly, fmt='.', yerr=sem_fr_randomly, capsize=5, color='black')
        ax2.grid()
        ax2.scatter(1+0.1*np.random.normal(size=len(mn_rate_mean_mean_randomly[conditions[0]])), mn_rate_mean_mean_randomly[conditions[0]])
        ax2.scatter(2+0.1*np.random.normal(size=len(mn_rate_mean_mean_randomly[conditions[1]])), mn_rate_mean_mean_randomly[conditions[1]])
        ax2.scatter(3+0.1*np.random.normal(size=len(mn_rate_mean_mean_randomly[conditions[2]])), mn_rate_mean_mean_randomly[conditions[2]])
        ax2.set_xticks([1,2,3])
        ax2.set_xticklabels([conditions[0], conditions[1], conditions[2]])
        ax2.set_ylabel('MN firing rate (pps)')
        ax2.set_title('Random Mode')
        add_significance_bars(ax2, mean_fr_randomly, p_values_randomly)

        # Adicionar legenda para significância apenas se houver alguma significância
        has_significance = any([
            any(p < 0.05 for p in p_values_regular.values()),
            any(p < 0.05 for p in p_values_randomly.values())
        ])

        if has_significance:
            fig.text(0.5, 0.02, '* p < 0.05, ** p < 0.01, *** p < 0.001', ha='center', fontsize=10)
            fig.subplots_adjust(bottom=0.1)  # Deixar espaço para a legenda
        else:
            fig.tight_layout()

        fig.savefig('diabetes/figuras/mn_firing_rate_comparison_combined.png', dpi=300, bbox_inches='tight')
        plt.close(fig)

        # Salvar dados em CSV para ambos os modos
        for mode, mn_rate_mean_mean, mn_rate_mean_CV in [('regular', mn_rate_mean_mean_regular, mn_rate_mean_CV_regular),
                                                        ('random', mn_rate_mean_mean_randomly, mn_rate_mean_CV_randomly)]:
            for cond in conditions:
                df = pd.DataFrame({
                    'firing_rate': mn_rate_mean_mean[cond].flatten(),
                    'ISI_CV': mn_rate_mean_CV[cond].flatten()
                })
                df.to_csv(f'diabetes/mn_firing_rate_{cond}_{mode}.csv', index=False)

            mean_fr = mean_fr_regular if mode == 'regular' else mean_fr_randomly
            sem_fr = sem_fr_regular if mode == 'regular' else sem_fr_randomly
            df_mean = pd.DataFrame({
                'condition': conditions,
                'mean_firing_rate': mean_fr,
                'sem_firing_rate': sem_fr
            })
            df_mean.to_csv(f'diabetes/mn_firing_rate_summary_{mode}.csv', index=False)

    def firing_rate(spiketrains, delta_t=0.00005, filtro_ordem=4, freq_corte=0.001, tempo_max=1000):
        """
        Função que gera o impulso de Dirac para os tempos de disparo de um neurônio.

        Parâmetros:
            spiketrains: Lista com os trens de disparo de neurônios.
            neuronio: Índice do neurônio a ser processado.
            delta_t: Intervalo de tempo.
            filtro_ordem : Ordem do filtro Butterworth.
            freq_corte: Frequência de corte normalizada para o filtro Butterworth.
            tempo_max: Tempo máximo para o eixo x (em milissegundos).
        """

        # Criação do vetor de tempo
        t = np.arange(0, tempo_max, delta_t)
        fr = np.zeros_like(t)

        # Adiciona o impulso de Dirac em cada tempo de disparo do neurônio
        idx = np.searchsorted(t, spiketrains/1000)
        idx = idx[idx<len(fr)]
        fr[idx] = 1/delta_t
        # Filtro Butterworth
        fs = 1/delta_t
        b, a = butter(filtro_ordem, freq_corte/(fs/2))

        # Aplicação do filtro
        fr = filtfilt(b, a, fr)
        fr[fr<0] = 0
        return fr, t




    return (
        compute_cv,
        compute_fr,
        firing_rate,
        plot_mn_fr_combined,
        select_mns_randomly,
        select_mns_regular,
    )


@app.cell
def _(
    compute_cv,
    compute_fr,
    conditions,
    np,
    path,
    plot_mn_fr_combined,
    select_mns_randomly,
    select_mns_regular,
    stats,
    t_end,
    t_start,
):

    def fr_analysis_combined(trials, pd=None, batch_name=''):
        force_level = 20

        # Executar análise para modo regular
        mn_rate_mean_mean_regular = dict()
        mn_rate_mean_mean_regular[conditions[0]] = np.array([]).reshape(-1,1)
        mn_rate_mean_mean_regular[conditions[1]] = np.array([]).reshape(-1,1)
        mn_rate_mean_mean_regular[conditions[2]] = np.array([]).reshape(-1,1)
        mn_rate_mean_CV_regular = dict()
        mn_rate_mean_CV_regular[conditions[0]] = np.array([]).reshape(-1,1)
        mn_rate_mean_CV_regular[conditions[1]] = np.array([]).reshape(-1,1)
        mn_rate_mean_CV_regular[conditions[2]] = np.array([]).reshape(-1,1)

        # Executar análise para modo randomly
        mn_rate_mean_mean_randomly = dict()
        mn_rate_mean_mean_randomly[conditions[0]] = np.array([]).reshape(-1,1)
        mn_rate_mean_mean_randomly[conditions[1]] = np.array([]).reshape(-1,1)
        mn_rate_mean_mean_randomly[conditions[2]] = np.array([]).reshape(-1,1)
        mn_rate_mean_CV_randomly = dict()
        mn_rate_mean_CV_randomly[conditions[0]] = np.array([]).reshape(-1,1)
        mn_rate_mean_CV_randomly[conditions[1]] = np.array([]).reshape(-1,1)
        mn_rate_mean_CV_randomly[conditions[2]] = np.array([]).reshape(-1,1)

        force_mean = 0
        CV_mean = 0
        n = 0

        for trial in trials:
            for condition in conditions:
                data = pd.read_csv(f'{path}spikedata_{condition}_{trial}_{batch_name}/cell_spike_ref_{force_level}.csv', delimiter=',')
                force = pd.read_csv(f'{path}force_{condition}_{trial}_{batch_name}/force_ref{force_level}.csv', delimiter=',').values
                data = data.values
                if condition == 'severe':
                    force = force[force[:,0]>t_start,1]
                    force_mean = force_mean + force
                    CV_mean = CV_mean +force.std()/force.mean()
                    n = n + 1

                # Análise modo regular
                selected_neurons_regular = select_mns_regular(data, t_start=t_start, t_end=t_end)
                mns_rate_mean_regular = compute_fr(selected_neurons_regular, data, t_start, t_end)
                ISI_CV_regular, _ = compute_cv(selected_neurons_regular, data, t_start, t_end)
                ISI_CV_regular = ISI_CV_regular[mns_rate_mean_regular>=0.01].reshape(-1,1)
                mns_rate_mean_regular = mns_rate_mean_regular[mns_rate_mean_regular>=0.01].reshape(-1,1)
                mn_rate_mean_mean_regular[condition] = np.vstack((mn_rate_mean_mean_regular[condition], mns_rate_mean_regular))
                mn_rate_mean_CV_regular[condition] = np.vstack((mn_rate_mean_CV_regular[condition], ISI_CV_regular))

                # Análise modo randomly
                selected_neurons_randomly = select_mns_randomly(data, t_start=t_start, t_end=t_end, size=6)
                mns_rate_mean_randomly = compute_fr(selected_neurons_randomly, data, t_start, t_end)
                ISI_CV_randomly, _ = compute_cv(selected_neurons_randomly, data, t_start, t_end)
                ISI_CV_randomly = ISI_CV_randomly[mns_rate_mean_randomly>=0.01].reshape(-1,1)
                mns_rate_mean_randomly = mns_rate_mean_randomly[mns_rate_mean_randomly>=0.01].reshape(-1,1)
                mn_rate_mean_mean_randomly[condition] = np.vstack((mn_rate_mean_mean_randomly[condition], mns_rate_mean_randomly))
                mn_rate_mean_CV_randomly[condition] = np.vstack((mn_rate_mean_CV_randomly[condition], ISI_CV_randomly))

        force_mean = force_mean/n
        unique_neurons = np.unique(data[:, 0])
        ISI_CV_all, _ = compute_cv(unique_neurons, data, t_start, t_end)
        print('Mean force: ', force_mean.mean(), 'CV force:', CV_mean)

        # Calcular p-values antes de criar os plots
        def calculate_pvalues_and_print(data, mode_name, conditions, stats):
            p_values = {}
            print(f"=== MODO {mode_name.upper()} ===")
            print(f'FR normal: {data["normal"].mean()}, FR low_affected: {data["low_affected"].mean()}, FR severe: {data["severe"].mean()}')

            _, p_values['normal_vs_severe'] = stats.ttest_ind(a=data[conditions[0]], b=data[conditions[2]])
            print("p-value normal-severe:", p_values['normal_vs_severe'])

            _, p_values['low_affected_vs_severe'] = stats.ttest_ind(a=data[conditions[1]], b=data[conditions[2]])
            print("p-value low_affected-severe:", p_values['low_affected_vs_severe'])

            _, p_values['normal_vs_low_affected'] = stats.ttest_ind(a=data[conditions[0]], b=data[conditions[1]])
            print("p-value normal-low_affected:", p_values['normal_vs_low_affected'])

            return p_values

        # Calcular p-values para ambos os modos
        p_values_regular = calculate_pvalues_and_print(mn_rate_mean_mean_regular, "regular", conditions, stats)
        p_values_randomly = calculate_pvalues_and_print(mn_rate_mean_mean_randomly, "random", conditions, stats)

        # Criar figura combinada com p-values
        plot_mn_fr_combined(mn_rate_mean_mean_regular, mn_rate_mean_CV_regular,
                           mn_rate_mean_mean_randomly, mn_rate_mean_CV_randomly,
                           conditions, pd, stats, p_values_regular, p_values_randomly)

    return (fr_analysis_combined,)


@app.cell
def _(batch_name, fr_analysis_combined, pd, trials):
    fr_analysis_combined(trials=trials, pd=pd, batch_name=batch_name)
    return


@app.cell
def _(
    batch_name,
    compute_cv,
    conditions,
    np,
    path,
    pd,
    plt,
    select_mns_regular,
    t_end,
    t_start,
):
    def mn_cv_friring_rate(trial):


        force_level = 20

        mn_rate_mean_mean = dict()
        mn_rate_mean_mean[conditions[0]] = np.array([]).reshape(-1,1)
        mn_rate_mean_mean[conditions[1]] = np.array([]).reshape(-1,1)
        mn_rate_mean_mean[conditions[2]] = np.array([]).reshape(-1,1)

        mn_rate_mean_CV = dict()
        mn_rate_mean_CV[conditions[0]] = np.array([]).reshape(-1,1)
        mn_rate_mean_CV[conditions[1]] = np.array([]).reshape(-1,1)
        mn_rate_mean_CV[conditions[2]] = np.array([]).reshape(-1,1)

        for condition in conditions:
            data = pd.read_csv(f'{path}spikedata_{condition}_{trial}_{batch_name}/cell_spike_ref_{force_level}.csv', delimiter=',')

            data = data.values

            selected_neurons = select_mns_regular(data, t_start=t_start, t_end=t_end)



            unique_neurons = np.unique(data[:, 0])
            ISI_CV_all, _ = compute_cv(unique_neurons, data, t_start, t_end)

            data = np.hstack((data, np.zeros((len(data),1))))
            for i in unique_neurons:
                data[data[:,0]==int(i),2] = ISI_CV_all[unique_neurons==int(i)]
            plt.figure()
            plt.scatter(data[:,1], data[:,0], c=data[:,2], cmap='Reds', vmin=0, vmax=1)
            selection = np.nonzero(np.in1d(data[:,0], selected_neurons))[0]
            # plt.scatter(data[selection,1], data[selection,0], c=data[selection,2], cmap='Blues', vmin=0, vmax=1)
            plt.title(condition)
            plt.show()
    return (mn_cv_friring_rate,)


@app.cell
def _(mn_cv_friring_rate):
    mn_cv_friring_rate(0)
    return


@app.cell
def _(
    batch_name,
    compute_cv,
    compute_fr,
    conditions,
    np,
    path,
    plt,
    select_mns_randomly,
    select_mns_regular,
    t_end,
    t_start,
):
    def fr_cv_combined(trials, pd=None):
        from mpl_toolkits.axes_grid1.inset_locator import inset_axes
        force_level = 20

        # Dados para modo random
        mn_rate_mean_mean_randomly = dict()
        mn_rate_mean_mean_randomly[conditions[0]] = np.array([]).reshape(-1,1)
        mn_rate_mean_mean_randomly[conditions[1]] = np.array([]).reshape(-1,1)
        mn_rate_mean_mean_randomly[conditions[2]] = np.array([]).reshape(-1,1)

        mn_rate_mean_CV_randomly = dict()
        mn_rate_mean_CV_randomly[conditions[0]] = np.array([]).reshape(-1,1)
        mn_rate_mean_CV_randomly[conditions[1]] = np.array([]).reshape(-1,1)
        mn_rate_mean_CV_randomly[conditions[2]] = np.array([]).reshape(-1,1)

        neurons_index_randomly = dict()
        neurons_index_randomly[conditions[0]] = np.array([]).reshape(-1,1)
        neurons_index_randomly[conditions[1]] = np.array([]).reshape(-1,1)
        neurons_index_randomly[conditions[2]] = np.array([]).reshape(-1,1)

        # Dados para modo regular
        mn_rate_mean_mean_regular = dict()
        mn_rate_mean_mean_regular[conditions[0]] = np.array([]).reshape(-1,1)
        mn_rate_mean_mean_regular[conditions[1]] = np.array([]).reshape(-1,1)
        mn_rate_mean_mean_regular[conditions[2]] = np.array([]).reshape(-1,1)

        mn_rate_mean_CV_regular = dict()
        mn_rate_mean_CV_regular[conditions[0]] = np.array([]).reshape(-1,1)
        mn_rate_mean_CV_regular[conditions[1]] = np.array([]).reshape(-1,1)
        mn_rate_mean_CV_regular[conditions[2]] = np.array([]).reshape(-1,1)

        neurons_index_regular = dict()
        neurons_index_regular[conditions[0]] = np.array([]).reshape(-1,1)
        neurons_index_regular[conditions[1]] = np.array([]).reshape(-1,1)
        neurons_index_regular[conditions[2]] = np.array([]).reshape(-1,1)

        color = dict()
        color[conditions[0]] = 'Blues'
        color[conditions[1]] = 'Oranges'
        color[conditions[2]] = 'Greens'

        for trial in trials:
            for condition in conditions:
                data = pd.read_csv(f'{path}spikedata_{condition}_{trial}_{batch_name}/cell_spike_ref_{force_level}.csv', delimiter=',')
                data = data.values

                # Análise modo random
                selected_neurons_randomly = select_mns_randomly(data, t_start=t_start, t_end=t_end, size=100)
                mns_rate_mean_randomly = compute_fr(selected_neurons_randomly, data, t_start, t_end)
                ISI_CV_randomly, _ = compute_cv(selected_neurons_randomly, data, t_start, t_end)
                ISI_CV_randomly = ISI_CV_randomly[mns_rate_mean_randomly>=0.01].reshape(-1,1)
                selected_neurons_randomly = selected_neurons_randomly[mns_rate_mean_randomly>=0.01].reshape(-1,1)
                mns_rate_mean_randomly = mns_rate_mean_randomly[mns_rate_mean_randomly>=0.01].reshape(-1,1)
                mn_rate_mean_mean_randomly[condition] = np.vstack((mn_rate_mean_mean_randomly[condition], mns_rate_mean_randomly))
                mn_rate_mean_CV_randomly[condition] = np.vstack((mn_rate_mean_CV_randomly[condition], ISI_CV_randomly))
                neurons_index_randomly[condition] = np.vstack((neurons_index_randomly[condition], selected_neurons_randomly.reshape(-1,1)))

                # Análise modo regular
                selected_neurons_regular = select_mns_regular(data, t_start=t_start, t_end=t_end)
                mns_rate_mean_regular = compute_fr(selected_neurons_regular, data, t_start, t_end)
                ISI_CV_regular, _ = compute_cv(selected_neurons_regular, data, t_start, t_end)
                ISI_CV_regular = ISI_CV_regular[mns_rate_mean_regular>=0.01].reshape(-1,1)
                selected_neurons_regular = selected_neurons_regular[mns_rate_mean_regular>=0.01].reshape(-1,1)
                mns_rate_mean_regular = mns_rate_mean_regular[mns_rate_mean_regular>=0.01].reshape(-1,1)
                mn_rate_mean_mean_regular[condition] = np.vstack((mn_rate_mean_mean_regular[condition], mns_rate_mean_regular))
                mn_rate_mean_CV_regular[condition] = np.vstack((mn_rate_mean_CV_regular[condition], ISI_CV_regular))
                neurons_index_regular[condition] = np.vstack((neurons_index_regular[condition], selected_neurons_regular.reshape(-1,1)))

        # Processar índices de neurônios para ambos os modos
        for mode_data in [neurons_index_randomly, neurons_index_regular]:
            mode_data[conditions[0]][mode_data[conditions[0]]> 120] = 250
            mode_data[conditions[1]][mode_data[conditions[1]]> 120] = 250
            mode_data[conditions[2]][mode_data[conditions[2]]> 120] = 250

            mode_data[conditions[0]][(mode_data[conditions[0]]<= 120) & (mode_data[conditions[0]]> 60)] = 100
            mode_data[conditions[1]][(mode_data[conditions[1]]<= 120) & (mode_data[conditions[1]]> 60)] = 100
            mode_data[conditions[2]][(mode_data[conditions[2]]<= 120) & (mode_data[conditions[2]]> 60)] = 100

            mode_data[conditions[0]][(mode_data[conditions[0]]< 60)] = 10
            mode_data[conditions[1]][(mode_data[conditions[1]]< 60)] = 10
            mode_data[conditions[2]][(mode_data[conditions[2]]< 60)] = 10

        # Criar figura principal com um único plot
        fig, ax = plt.subplots(figsize=(10, 8))

        # Plot principal com dados do modo random (como estava originalmente)
        ax.scatter(mn_rate_mean_CV_randomly[conditions[0]], mn_rate_mean_mean_randomly[conditions[0]], c=neurons_index_randomly[conditions[0]], cmap=color[conditions[0]], vmin=1, vmax=250, alpha=0.7)
        ax.scatter(mn_rate_mean_CV_randomly[conditions[1]], mn_rate_mean_mean_randomly[conditions[1]], c=neurons_index_randomly[conditions[1]], cmap=color[conditions[1]], vmin=1, vmax=250, alpha=0.7)
        ax.scatter(mn_rate_mean_CV_randomly[conditions[2]], mn_rate_mean_mean_randomly[conditions[2]], c=neurons_index_randomly[conditions[2]], cmap=color[conditions[2]], vmin=1, vmax=250, alpha=0.7)

        ax.set_xlabel('ISI CoV')
        ax.set_ylabel('Firing rate mean (pps)')
        ax.set_title('CV vs Firing Rate - Random Mode')
        ax.grid(True, linestyle='--', alpha=0.7)

        # Legenda com cores fixas
        legend_labels = [conditions[0], conditions[1], conditions[2]]
        legend_colors = ['blue', 'orange', 'green']
        legend_handles = []
        for legend_color, label in zip(legend_colors, legend_labels):
            legend_handles.append(ax.scatter([], [], color=legend_color, label=label))
        ax.legend(handles=legend_handles, loc='upper left')

        # Inset 1: Zoom para firing rates entre 5-15 Hz e CV < 0.3
        axins1 = inset_axes(ax, width="45%", height="35%", loc='center right', bbox_to_anchor=(-0.03, 0.3, 1, 1), bbox_transform=ax.transAxes)
        axins1.scatter(mn_rate_mean_CV_randomly[conditions[0]], mn_rate_mean_mean_randomly[conditions[0]], c=neurons_index_randomly[conditions[0]], cmap=color[conditions[0]], vmin=1, vmax=250, alpha=0.7, s=25)
        axins1.scatter(mn_rate_mean_CV_randomly[conditions[1]], mn_rate_mean_mean_randomly[conditions[1]], c=neurons_index_randomly[conditions[1]], cmap=color[conditions[1]], vmin=1, vmax=250, alpha=0.7, s=25)
        axins1.scatter(mn_rate_mean_CV_randomly[conditions[2]], mn_rate_mean_mean_randomly[conditions[2]], c=neurons_index_randomly[conditions[2]], cmap=color[conditions[2]], vmin=1, vmax=250, alpha=0.7, s=25)
        axins1.set_xlim(0, 0.3)
        axins1.set_ylim(5, 15)
        axins1.set_xlabel('ISI CoV', fontsize=9)
        axins1.set_ylabel('FR (pps)', fontsize=9)
        axins1.tick_params(axis='both', which='major', labelsize=9)
        axins1.grid(True, linestyle='--', alpha=0.7)
        # axins1.set_title('5-15 Hz, CoV<0.3', fontsize=10)

        # Inset 2: Zoom para firing rates > 15 Hz
        axins2 = inset_axes(ax, width="45%", height="35%", loc='center right', bbox_to_anchor=(-0.03, -0.13, 1, 1), bbox_transform=ax.transAxes)
        axins2.scatter(mn_rate_mean_CV_randomly[conditions[0]], mn_rate_mean_mean_randomly[conditions[0]], c=neurons_index_randomly[conditions[0]], cmap=color[conditions[0]], vmin=1, vmax=250, alpha=0.7, s=25)
        axins2.scatter(mn_rate_mean_CV_randomly[conditions[1]], mn_rate_mean_mean_randomly[conditions[1]], c=neurons_index_randomly[conditions[1]], cmap=color[conditions[1]], vmin=1, vmax=250, alpha=0.7, s=25)
        axins2.scatter(mn_rate_mean_CV_randomly[conditions[2]], mn_rate_mean_mean_randomly[conditions[2]], c=neurons_index_randomly[conditions[2]], cmap=color[conditions[2]], vmin=1, vmax=250, alpha=0.7, s=25)
        axins2.set_xlim(0, 0.25)
        axins2.set_ylim(15, 30)
        axins2.set_xlabel('ISI CoV', fontsize=9)
        axins2.set_ylabel('FR (pps)', fontsize=9)
        axins2.tick_params(axis='both', which='major', labelsize=9)
        axins2.grid(True, linestyle='--', alpha=0.7)
        # axins2.set_title('FR > 15 Hz', fontsize=9)

        fig.tight_layout()
        fig.savefig('diabetes/figuras/fr_cv_scatter_combined.png', bbox_inches='tight', dpi=300)
        plt.close(fig)

        # Salvar dados em CSV para ambos os modos
        import os
        os.makedirs('diabetes/figuras', exist_ok=True)
        for mode, mn_rate_mean_mean, mn_rate_mean_CV, neurons_index in [
            ('random', mn_rate_mean_mean_randomly, mn_rate_mean_CV_randomly, neurons_index_randomly),
            ('regular', mn_rate_mean_mean_regular, mn_rate_mean_CV_regular, neurons_index_regular)
        ]:
            for cond in conditions:
                df = pd.DataFrame({
                    'firing_rate': mn_rate_mean_mean[cond].flatten(),
                    'ISI_CV': mn_rate_mean_CV[cond].flatten(),
                    'neuron_index': neurons_index[cond].flatten()
                })
                df.to_csv(f'diabetes/fr_cv_{cond}_{mode}.csv', index=False)
    return (fr_cv_combined,)


@app.cell
def _(fr_cv_combined, pd, trials):
    fr_cv_combined(trials, pd=pd)
    return


@app.cell
def _(batch_name, conditions, detrend, path, plt, t_start, welch):
    def force_spectrum(trials, pd=None):
        import os
        force_level = 20
        force = dict()
        n = dict()
        for condition in conditions:
            force[condition] = 0
            n[condition] = 0
        for trial in trials:
            for condition in conditions:
                force_data = pd.read_csv(f'{path}force_{condition}_{trial}_{batch_name}_long/force_ref{force_level}.csv', delimiter=',').values
                force[condition] = force[condition] + force_data[force_data[:,0]> t_start, 1]
        Pforce = dict()
        nperseg = 176000
        for condition in conditions:
            force[condition] = force[condition]/len(trials)
            f, Pforce[condition] = welch(detrend(force[condition]), fs=1/0.00005, nperseg=nperseg, nfft=nperseg, detrend=False)
        os.makedirs('diabetes/figuras', exist_ok=True)
        # Criar uma única figura com dois subplots lado a lado
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

        # Espectro de força
        for condition in conditions:
            ax1.plot(f, (Pforce[condition])/(Pforce[condition]).max(), label=condition)
        ax1.legend([conditions[0], conditions[1], conditions[2]])
        ax1.set_xlim(0, 5)
        ax1.set_title('Force Spectrum')
        ax1.set_xlabel('Frequency (Hz)')
        ax1.set_ylabel('Normalized Power')
        ax1.grid(True, linestyle='--', alpha=0.7)

        # Força no tempo
        for condition in conditions:
            ax2.plot(force[condition], label=condition)
        ax2.set_title('Force (mean)')
        ax2.set_xlabel('Sample')
        ax2.set_ylabel('Force (a.u.)')
        ax2.legend()
        ax2.grid(True, linestyle='--', alpha=0.7)

        fig.tight_layout()
        fig.savefig('diabetes/figuras/force_spectrum_combined.png', dpi=300, bbox_inches='tight')
        plt.close(fig)
        # Salvar dados em CSV
        for cond in conditions:
            df = pd.DataFrame({
                'frequency': f,
                'normalized_power': (Pforce[cond])/(Pforce[cond]).max()
            })
            df.to_csv(f'diabetes/force_spectrum_{cond}.csv', index=False)
    return (force_spectrum,)


@app.cell
def _(force_spectrum, pd, trials_long):
    force_spectrum(trials=trials_long, pd=pd)
    return


@app.cell
def _(
    batch_name,
    conditions,
    csd,
    detrend,
    firing_rate,
    np,
    path,
    plt,
    select_mns_randomly,
    select_mns_regular,
    t_end,
    t_start,
    welch,
):
    def coherence_mu_combined(trials, pd=None):
        import os
        force_level = 20

        divisions = 2
        nperseg = 176000//divisions

        # Dados para ambos os modos
        results = {}
        for mode in ['randomly', 'regular']:
            Prate = dict()
            Pfr = dict()
            Prate_fr = dict()
            n = dict()
            for condition in conditions:
                Prate[condition]= 0
                Pfr[condition] = 0
                Prate_fr[condition] = 0
                n[condition] = 0

            for trial in trials:
                for condition in conditions:
                    data = pd.read_csv(f'{path}spikedata_{condition}_{trial}_{batch_name}_long/cell_spike_ref_{force_level}.csv', delimiter=',')
                    source_data = pd.read_csv(f'{path}spikefeedbacksource_{condition}_{trial}_{batch_name}_long/spike_data_ref_{force_level}.csv', delimiter=',')
                    data = data.values
                    source_data['spike_time'] = source_data['spike_time'].str.replace(' ms', '')
                    source_data['spike_time'] = source_data['spike_time'].astype('float')
                    source_data = source_data.values
                    steady_data = data[(data[:, 1] >= t_start) & (data[:, 1] <= 180000)]
                    steady_source_data = source_data[(source_data[:, 1] >= t_start) & (source_data[:, 1] <= 180000)]
                    if mode == 'randomly': selected_neurons = select_mns_randomly(data, t_start=t_start, t_end=t_end, size=1)
                    if mode == 'regular': selected_neurons = select_mns_regular(data, t_start=t_start, t_end=t_end)
                    rate = 0
                    dc_neurons = np.unique(steady_source_data[:,0])
                    np.random.shuffle(dc_neurons)
                    for dc in dc_neurons[:50]:
                        fr_source, _ = firing_rate(steady_source_data[steady_source_data[:,0]==dc,1]-t_start,
                                                   delta_t=0.00005, filtro_ordem=4, freq_corte=500, tempo_max=(180000-t_start)/1000)
                        rate = rate + fr_source
                    rate = rate/50
                    for neuron in selected_neurons:
                        fr_new, _ =  firing_rate(steady_data[steady_data[:,0]==neuron,1]-t_start, delta_t=0.00005,
                                                 filtro_ordem=4, freq_corte=5, tempo_max=(180000-t_start)/1000)
                        f, Sfr = welch(detrend(fr_new), fs=1/0.00005, nperseg=nperseg, detrend=None)
                        Pfr[condition] = Pfr[condition] + Sfr

                        f, Srate = welch(detrend(rate), fs=1/0.00005, nperseg=nperseg, detrend=None)
                        Prate[condition] = Prate[condition] + Srate
                        f, Sratefr = csd(detrend(rate), detrend(fr_new), fs=1/0.00005, nperseg=nperseg, detrend=None)
                        Prate_fr[condition] = Prate_fr[condition] + Sratefr
                        n[condition] = n[condition] + 1

            Coh = dict()
            for condition in conditions:
                Pfr[condition] = Pfr[condition]/n[condition]
                Prate[condition] = Prate[condition]/n[condition]
                Prate_fr[condition] = Prate_fr[condition]/n[condition]
                Coh[condition] = np.abs(Prate_fr[condition])**2/(Prate[condition]*Pfr[condition]+1e-6)

            results[mode] = {'Coh': Coh, 'Pfr': Pfr, 'f': f}

        os.makedirs('diabetes/figuras', exist_ok=True)
        # Criar figura com dois subplots lado a lado (um para cada modo)
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

        # Plot para modo random
        for condition in conditions:
            ax1.plot(results['randomly']['f'], results['randomly']['Coh'][condition], label=condition)
        ax1.set_title('Coherence - Random Mode')
        ax1.set_xlim(results['randomly']['f'][0], 5)
        ax1.set_xlabel('Frequency (Hz)')
        ax1.set_ylabel('Coherence')
        ax1.legend()
        ax1.grid(True, linestyle='--', alpha=0.7)

        # Plot para modo regular
        for condition in conditions:
            ax2.plot(results['regular']['f'], results['regular']['Coh'][condition], label=condition)
        ax2.set_title('Coherence - Regular Mode')
        ax2.set_xlim(results['regular']['f'][0], 5)
        ax2.set_xlabel('Frequency (Hz)')
        ax2.set_ylabel('Coherence')
        ax2.legend()
        ax2.grid(True, linestyle='--', alpha=0.7)

        fig.tight_layout()
        fig.savefig('diabetes/figuras/coherence_mu_combined.png', dpi=300, bbox_inches='tight')
        plt.close(fig)

        # Salvar dados em CSV para ambos os modos
        for mode in ['random', 'regular']:
            mode_key = 'randomly' if mode == 'random' else 'regular'
            for cond in conditions:
                df = pd.DataFrame({
                    'frequency': results[mode_key]['f'],
                    'coherence': results[mode_key]['Coh'][cond]
                })
                df.to_csv(f'diabetes/coherence_mu_{cond}_{mode}.csv', index=False)
    return (coherence_mu_combined,)


@app.cell
def _(coherence_mu_combined, pd, trials_long):
    coherence_mu_combined(trials=trials_long, pd=pd)
    return


@app.cell
def _(
    batch_name,
    conditions,
    detrend,
    firing_rate,
    path,
    plt,
    select_mns_randomly,
    select_mns_regular,
    welch,
):
    def fr_spectra_combined(trials, pd=None):
        import os
        force_level = 20
        t_start = 4000
        t_end = 180000
        nperseg = 176000

        # Dados para ambos os modos
        results = {}
        for mode in ['randomly', 'regular']:
            fr_low = dict()
            fr_medium = dict()
            fr_high = dict()
            n_low = dict()
            n_medium = dict()
            n_high = dict()
            Pfr_low = dict()
            Pfr_medium = dict()
            Pfr_high = dict()

            for condition in conditions:
                fr_low[condition] = 0
                fr_medium[condition] = 0
                fr_high[condition] = 0
                n_low[condition] = 0
                n_medium[condition] = 0
                n_high[condition] = 0

            for trial in trials:
                for condition in conditions:
                    data = pd.read_csv(f'{path}spikedata_{condition}_{trial}_{batch_name}_long/cell_spike_ref_{force_level}.csv', delimiter=',')
                    data = data.values
                    steady_data = data[(data[:, 1] >= t_start) & (data[:, 1] <= t_end)]
                    if mode == 'randomly': selected_neurons = select_mns_randomly(data, t_start=t_start, t_end=10000, size=100)
                    if mode == 'regular': selected_neurons = select_mns_regular(data, t_start=t_start, t_end=10000)
                    for neuron in selected_neurons:
                        fr_new, _ = firing_rate(steady_data[steady_data[:,0]==neuron,1]-t_start, delta_t=0.00005, filtro_ordem=4, freq_corte=100, tempo_max=(t_end-t_start)/1000)
                        if fr_new.mean() > 0.1:
                            if neuron <= 85:
                                fr_low[condition] = fr_low[condition] + fr_new
                                n_low[condition] = n_low[condition] + 1
                            elif neuron <= 110:
                                fr_medium[condition] = fr_medium[condition] + fr_new
                                n_medium[condition] = n_medium[condition] + 1
                            else:
                                fr_high[condition] = fr_high[condition] + fr_new
                                n_high[condition] = n_high[condition] + 1

            for condition in conditions:
                fr_low[condition] = fr_low[condition]/n_low[condition]
                fr_medium[condition] = fr_medium[condition]/n_medium[condition]
                fr_high[condition] = fr_high[condition]/n_high[condition]
                f, Pfr_low[condition] = welch(detrend(fr_low[condition]), fs=1/0.00005, nperseg=nperseg, nfft = nperseg, detrend=False)
                f, Pfr_medium[condition] = welch(detrend(fr_medium[condition]), fs=1/0.00005, nperseg=nperseg, nfft = nperseg, detrend=False)
                f, Pfr_high[condition] = welch(detrend(fr_high[condition]), fs=1/0.00005, nperseg=nperseg, nfft = nperseg, detrend=False)

            results[mode] = {
                'fr_low': fr_low, 'fr_medium': fr_medium, 'fr_high': fr_high,
                'Pfr_low': Pfr_low, 'Pfr_medium': Pfr_medium, 'Pfr_high': Pfr_high,
                'f': f
            }

        os.makedirs('diabetes/figuras', exist_ok=True)
        # Para cada tipo (low, medium, high), criar figura com dois subplots lado a lado (um para cada modo)
        tipos = [('low', 'Pfr_low', 'fr_low'), ('medium', 'Pfr_medium', 'fr_medium'), ('high', 'Pfr_high', 'fr_high')]
        for tipo, pfr_key, fr_key in tipos:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 10))

            # Espectro de firing rate - modo random
            for condition in conditions:
                ax1.plot(results['randomly']['f'], results['randomly'][pfr_key][condition], label=condition)
            ax1.set_title(f'FR Spectrum - {tipo} (Random)')
            ax1.set_xlim(results['randomly']['f'][0], 100)
            ax1.legend()
            ax1.set_xlabel('Frequency (Hz)')
            ax1.set_ylabel('Power')
            ax1.grid(True, linestyle='--', alpha=0.7)

            # Espectro de firing rate - modo regular
            for condition in conditions:
                ax2.plot(results['regular']['f'], results['regular'][pfr_key][condition], label=condition)
            ax2.set_title(f'FR Spectrum - {tipo} (Regular)')
            ax2.set_xlim(results['regular']['f'][0], 100)
            ax2.legend()
            ax2.set_xlabel('Frequency (Hz)')
            ax2.set_ylabel('Power')
            ax2.grid(True, linestyle='--', alpha=0.7)

            # Sinal de firing rate no tempo - modo random
            for condition in conditions:
                ax3.plot(results['randomly'][fr_key][condition], label=condition)
            ax3.set_title(f'FR (mean) - {tipo} (Random)')
            ax3.set_xlabel('Sample')
            ax3.set_ylabel('FR (a.u.)')
            ax3.legend()
            ax3.grid(True, linestyle='--', alpha=0.7)

            # Sinal de firing rate no tempo - modo regular
            for condition in conditions:
                ax4.plot(results['regular'][fr_key][condition], label=condition)
            ax4.set_title(f'FR (mean) - {tipo} (Regular)')
            ax4.set_xlabel('Sample')
            ax4.set_ylabel('FR (a.u.)')
            ax4.legend()
            ax4.grid(True, linestyle='--', alpha=0.7)

            fig.tight_layout()
            fig.savefig(f'diabetes/figuras/fr_spectra_{tipo}_combined.png', dpi=300, bbox_inches='tight')
            plt.close(fig)

        # Salvar dados em CSV para ambos os modos
        for mode in ['random', 'regular']:
            mode_key = 'randomly' if mode == 'random' else 'regular'
            for cond in conditions:
                df_low = pd.DataFrame({'frequency': results[mode_key]['f'], 'power': results[mode_key]['Pfr_low'][cond]})
                df_medium = pd.DataFrame({'frequency': results[mode_key]['f'], 'power': results[mode_key]['Pfr_medium'][cond]})
                df_high = pd.DataFrame({'frequency': results[mode_key]['f'], 'power': results[mode_key]['Pfr_high'][cond]})
                df_low.to_csv(f'diabetes/fr_spectra_low_{cond}_{mode}.csv', index=False)
                df_medium.to_csv(f'diabetes/fr_spectra_medium_{cond}_{mode}.csv', index=False)
                df_high.to_csv(f'diabetes/fr_spectra_high_{cond}_{mode}.csv', index=False)
    return (fr_spectra_combined,)


@app.cell
def _(fr_spectra_combined, pd, trials_long):
    fr_spectra_combined(trials_long, pd=pd)
    return


@app.cell
def _(
    batch_name,
    conditions,
    np,
    path,
    pd,
    plt,
    select_mns_randomly,
    select_mns_regular,
):
    def what_mn_selected_combined(trial):
        import os
        force_level = 20
        t_start = 4000
        t_end = 10000

        os.makedirs('diabetes/figuras', exist_ok=True)

        for condition in conditions:
            data = pd.read_csv(f'{path}spikedata_{condition}_{trial}_{batch_name}/cell_spike_ref_{force_level}.csv', delimiter=',')
            data = data.values

            # Seleção para modo regular
            selected_neurons_regular = select_mns_regular(data, t_start=t_start, t_end=t_end)

            # Seleção para modo randomly
            selected_neurons_randomly = select_mns_randomly(data, t_start=t_start, t_end=t_end, size=4)

            # Criar figura com dois subplots lado a lado
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

            # Plot para modo regular
            ax1.plot(data[:,1], data[:,0], '.y', alpha=0.5, label='All neurons')
            ax1.plot(data[np.nonzero(np.in1d(data[:,0], selected_neurons_regular))[0],1],
                    data[np.nonzero(np.in1d(data[:,0], selected_neurons_regular))[0],0], '.b', label='Selected neurons')
            ax1.set_title(f'{condition} - Regular Mode')
            ax1.set_xlabel('Time (ms)')
            ax1.set_ylabel('Neuron ID')
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # Plot para modo random
            ax2.plot(data[:,1], data[:,0], '.y', alpha=0.5, label='All neurons')
            ax2.plot(data[np.nonzero(np.in1d(data[:,0], selected_neurons_randomly))[0],1],
                    data[np.nonzero(np.in1d(data[:,0], selected_neurons_randomly))[0],0], '.r', label='Selected neurons')
            ax2.set_title(f'{condition} - Random Mode')
            ax2.set_xlabel('Time (ms)')
            ax2.set_ylabel('Neuron ID')
            ax2.legend()
            ax2.grid(True, alpha=0.3)

            fig.tight_layout()
            fig.savefig(f'diabetes/figuras/what_mn_selected_{condition}_{trial}_combined.png', dpi=300, bbox_inches='tight')
            plt.close(fig)
    return (what_mn_selected_combined,)


@app.cell
def _(what_mn_selected_combined):
    what_mn_selected_combined(30)
    return


@app.cell
def _():
    return


if __name__ == "__main__":
    app.run()
